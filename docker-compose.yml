version: '3.7'
services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '${PORT_EXPOSE}:${APP_PORT}'
    environment:
      - DATABASE_URL=**********************************/sit-db
      - MONGODB_URI=*************************************************************************
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
      - thread-db
    networks:
      - app-network
    container_name: sit-api
  db:
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: sit-db
    ports:
      - '5434:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    container_name: sit-db
  thread-db:
    image: mongo:7.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: mongodb_user
      MONG<PERSON>_INITDB_ROOT_PASSWORD: 123456
      MONGO_INITDB_DATABASE: sit_thread
    ports:
      - '27017:27017'
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    container_name: sit-mongodb
    restart: unless-stopped
  redis:
    image: redis:6.2
    ports:
      - '6379:6379'
    networks:
      - app-network
    container_name: sit-redis
    restart: unless-stopped
volumes:
  postgres_data:
  mongodb_data:
networks:
  app-network:
    driver: bridge
