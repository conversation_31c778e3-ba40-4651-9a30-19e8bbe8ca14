import { Server, Socket } from 'socket.io';
import { ThreadsService } from '../modules/threads/threads.service';
import { CustomLogger } from '../common/logging';

type JoinThreadRequest = {
  threadId: string;
  userId: string;
};

type SendMessageRequest = {
  threadId: string;
  userId: string;
  message: {
    status: string;
    message: string;
    timestamp: string;
  };
};

export class ThreadGateway {
  private threadsService: ThreadsService;

  constructor() {
    // Create a simple logger for WebSocket gateway
    const logger = {
      log: (message: string) => console.log(message),
      error: (message: string, _meta?: any, stack?: string) =>
        console.error(message, stack),
      warn: (message: string) => console.warn(message),
      debug: (message: string) => console.debug(message),
    };
    this.threadsService = new ThreadsService(logger as CustomLogger);
  }

  // Handle connection and setup event listeners
  public handleConnection(io: Server, socket: Socket) {
    console.log(`Thread Gateway: ${socket.id} connected`);

    // Handle join thread
    socket.on('joinThread', async (data: any) => {
      try {
        await this.threadsService.handleJoinThread(
          io,
          socket,
          data as JoinThreadRequest,
        );
      } catch (error) {
        console.error('JoinThread error:', error);
        socket.emit(
          'error',
          JSON.stringify({
            type: 'UnexpectedError',
            message: 'Failed to join thread',
          }),
        );
      }
    });

    // Handle send message
    socket.on('sendThreadMessage', async (data: any) => {
      try {
        await this.threadsService.handleSendMessage(
          io,
          socket,
          data as SendMessageRequest,
        );
      } catch (error) {
        console.error('SendThreadMessage error:', error);
        socket.emit(
          'error',
          JSON.stringify({
            type: 'UnexpectedError',
            message: 'Failed to send message',
          }),
        );
      }
    });

    // Handle leave thread
    socket.on('leaveThread', (threadId: string) => {
      try {
        void socket.leave(threadId);
        console.log(`User ${socket.id} left thread ${threadId}`);

        socket.emit(
          'leaveThreadResult',
          JSON.stringify({
            type: 'LeaveThreadSuccess',
            result: { threadId },
          }),
        );
      } catch (error) {
        console.error('LeaveThread error:', error);
        socket.emit(
          'error',
          JSON.stringify({
            type: 'UnexpectedError',
            message: 'Failed to leave thread',
          }),
        );
      }
    });

    // Handle typing indicator
    socket.on(
      'typing',
      (data: { threadId: string; userId: string; isTyping: boolean }) => {
        try {
          const { threadId, userId, isTyping } = data;

          if (isTyping) {
            socket.to(threadId).emit('userTyping', {
              userId,
              threadId,
              isTyping: true,
            });
          } else {
            socket.to(threadId).emit('userTyping', {
              userId,
              threadId,
              isTyping: false,
            });
          }
        } catch (error) {
          console.error('Typing indicator error:', error);
        }
      },
    );

    // Handle read receipts
    socket.on(
      'markAsRead',
      (data: { threadId: string; messageId: string; userId: string }) => {
        try {
          const { threadId, messageId, userId } = data;

          socket.to(threadId).emit('messageRead', {
            messageId,
            userId,
            threadId,
            readAt: new Date().toISOString(),
          });
        } catch (error) {
          console.error('Mark as read error:', error);
        }
      },
    );

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`Thread Gateway: ${socket.id} disconnected`);
    });
  }

  // Method to broadcast to all users in a thread
  public broadcastToThread(
    io: Server,
    threadId: string,
    event: string,
    data: any,
  ) {
    io.to(threadId).emit(event, data);
  }

  // Method to send to specific user
  public sendToUser(io: Server, userId: string, event: string, data: any) {
    io.to(userId).emit(event, data);
  }

  // Method to get online users in a thread
  public getOnlineUsersInThread(io: Server, threadId: string) {
    const room = io.sockets.adapter.rooms.get(threadId);
    return room ? Array.from(room) : [];
  }
}
