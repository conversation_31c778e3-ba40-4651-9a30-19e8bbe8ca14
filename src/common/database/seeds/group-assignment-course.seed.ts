import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { GroupAssignmentCourse } from '@modules/group-assignment-courses/entities/group-assignment-course.entity';

export async function seedGroupAssignmentCourses(
  dataSource: DataSource,
  users: User[],
  courses: Course[],
  groupAssignments: GroupAssignment[],
): Promise<GroupAssignmentCourse[]> {
  console.log('🌱 Seeding group assignment courses...');
  const groupAssignmentCourseRepository = dataSource.getRepository(
    GroupAssignmentCourse,
  );

  // Check if group assignment courses already exist
  const existingGroupAssignmentCourses =
    await groupAssignmentCourseRepository.find();
  if (existingGroupAssignmentCourses.length > 0) {
    console.log(
      '✅ Group assignment courses already exist, skipping group assignment course seeding',
    );
    return existingGroupAssignmentCourses;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;

  const groupAssignmentCourses = [
    {
      id: uuidv4(),
      name: 'Software Engineering Project Course',
      description:
        'Advanced course focusing on real-world software development practices and methodologies.',
      groupId: groupAssignments[0].id,
      courseId: courses[0].id,
      createdBy: instructor.id,
    },
    {
      id: uuidv4(),
      name: 'Database Systems Project Course',
      description:
        'Practical database design and implementation course with hands-on projects.',
      groupId: groupAssignments[1].id,
      courseId: courses[1].id,
      createdBy: instructor.id,
    },
  ];

  const savedGroupAssignmentCourses =
    await groupAssignmentCourseRepository.save(groupAssignmentCourses);
  console.log(
    `✅ Created ${savedGroupAssignmentCourses.length} group assignment courses`,
  );
  return savedGroupAssignmentCourses;
}
