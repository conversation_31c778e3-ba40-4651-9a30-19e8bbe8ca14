import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { Document } from '@modules/documents/entities/document.entity';

export async function seedDocuments(
  dataSource: DataSource,
  users: User[],
  groupAssignments: GroupAssignment[],
): Promise<Document[]> {
  console.log('🌱 Seeding documents...');
  const documentRepository = dataSource.getRepository(Document);

  // Check if documents already exist
  const existingDocuments = await documentRepository.find();
  if (existingDocuments.length > 0) {
    console.log('✅ Documents already exist, skipping document seeding');
    return existingDocuments;
  }

  const instructor = users.find((u) => u.name === 'Dr. John')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  const documents = [
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ecommerce-requirements.pdf',
      fileName: 'Project Requirements Document.pdf',
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/library-schema.pdf',
      fileName: 'Database Schema Design.pdf',
      uploaderId: student1.id,
      groupId: groupAssignments[1].id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ml-training-data.csv',
      fileName: 'ML Model Training Data.csv',
      uploaderId: student2.id,
      groupId: groupAssignments[2].id,
    },
  ];

  const savedDocuments = await documentRepository.save(documents);
  console.log(`✅ Created ${savedDocuments.length} documents`);
  return savedDocuments;
}
