import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';

export async function seedUsers(dataSource: DataSource): Promise<User[]> {
  console.log('🌱 Seeding users...');
  const userRepository = dataSource.getRepository(User);

  // Check if users already exist by counting
  const userCount = await userRepository.count();
  if (userCount > 0) {
    console.log('✅ Users already exist, skipping user seeding');
    const existingUsers = await userRepository.find();
    return existingUsers;
  }

  const users = [
    {
      id: uuidv4(),
      name: 'Dr. <PERSON>',
      email: '<EMAIL>',
      isAdmin: true,
      isInstructor: true,
    },
    {
      id: uuidv4(),
      name: '<PERSON>',
      email: '<EMAIL>',
      isAdmin: false,
      isInstructor: false,
    },
    {
      id: uuidv4(),
      name: '<PERSON>',
      email: '<EMAIL>',
      isAdmin: false,
      isInstructor: false,
    },
  ];

  const savedUsers = await userRepository.save(users);
  console.log(`✅ Created ${savedUsers.length} users`);
  return savedUsers;
}
