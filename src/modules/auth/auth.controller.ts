// src/auth/auth.controller.ts
import { Controller, Post, Body, Get, Req, Res, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthDto } from './dto/auth.dto';
import { AuthGuard } from '@nestjs/passport';

@Controller('v1/auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  async loginUser(@Body() user: AuthDto) {
    return {};
  }

  @Post('refresh-token')
  async refreshAccessToken(
    @Body('refresh-accesstoken') refreshTokenDto: string,
  ) {
    return {};
  }
}
