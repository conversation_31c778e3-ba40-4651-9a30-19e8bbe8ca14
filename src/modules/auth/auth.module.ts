// src/auth/auth.module.ts
import { Module, OnModuleInit } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { AdminAuthController } from './admin-auth.controller';
import { JwtStrategy } from './jwt.strategy';
import { PassportModule } from '@nestjs/passport';
import { UsersModule } from '../users/users.module';
import { CustomLogger } from '../../common/logging';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { JwtService } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CustomAdfsStrategy } from './custom-adfs-strategy';
import * as passport from 'passport';
import { AdfsController } from './adfs.controller';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    UsersModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>(
          'AUTH_JWT_SECRET',
          '9d4dc9dc-77a6-4ad1-a856-60fa431b7e02',
        ),
        signOptions: {
          expiresIn: configService.get<string>(
            'AUTH_JWT_TOKEN_EXPIRES_IN',
            '24h',
          ),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AuthService, JwtStrategy, CustomLogger, JwtAuthGuard],
  controllers: [AuthController, AdminAuthController, AdfsController],
  exports: [AuthService, JwtAuthGuard],
})
export class AuthModule implements OnModuleInit {
  onModuleInit() {
    passport.use(
      new CustomAdfsStrategy(
        {
          authorizationURL: process.env.ADFS_AUTHORIZATION_URL!,
          tokenURL: process.env.ADFS_TOKEN_URL!,
          clientID: process.env.ADFS_CLIENT_ID!,
          clientSecret: process.env.ADFS_CLIENT_SECRET!,
          callbackURL: process.env.ADFS_CALLBACK_URL!,
          resource: process.env.ADFS_AUDIENCE!,
        },
        async (accessToken, refreshToken, profile, done) => {
          done(null, profile);
        }
      )
    );
  }
}
