import { Controller, Get, Req, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';

@Controller()
export class AdfsController {
  constructor(private readonly authService: AuthService) {}

  @Get('/oauth2/login')
  @UseGuards(AuthGuard('adfs'))
  async legacyAdfsLogin() {
    // Passport will automatically redirect to ADFS
  }

  @Get('/oauth2/callback')
  @UseGuards(AuthGuard('adfs'))
  async legacyAdfsCallback(@Req() req, @Res() res) {
    if (req.user) {
      const result = await this.authService.handleADFSLogin(req.user);
      const redirectUrl = `${process.env.ADFS_FE_CALLBACK_SUCCESS}?token=${result.accessToken}`;
      res.redirect(redirectUrl);
    } else {
      res.redirect(process.env.ADFS_FE_CALLBACK_ERROR || '/');
    }
  }
} 