import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';

@Entity('courses')
export class Course extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  // Relationships
  @OneToMany('GroupAssignment', 'course')
  groupAssignments: any[];

  @OneToMany('CourseUser', 'course')
  courseUsers: any[];

  @OneToMany('GroupAssignmentCourse', 'course')
  groupAssignmentCourses: any[];
}
