import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';

@Entity('group_assignments')
export class GroupAssignment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({ type: 'uuid' })
  courseId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne('Course', 'groupAssignments', {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  course: any;

  @OneToMany('GroupUser', 'group')
  groupUsers: any[];

  @OneToMany('Document', 'group')
  documents: any[];

  @OneToMany('GroupAssignmentCourse', 'group')
  groupAssignmentCourses: any[];
}
