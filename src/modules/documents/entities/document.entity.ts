import { <PERSON><PERSON><PERSON>, Colum<PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';

@Entity('documents')
export class Document extends BaseEntity {
  @Column({ type: 'varchar', length: 500 })
  fileUrl: string;

  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  uploaderId: string;

  @Column({ type: 'uuid' })
  groupId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  uploader: User;

  @ManyToOne('GroupAssignment', 'documents', { onDelete: 'CASCADE' })
  @JoinColumn()
  group: any;
}
