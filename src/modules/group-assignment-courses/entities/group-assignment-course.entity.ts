import { Enti<PERSON>, Column, <PERSON>To<PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';

@Entity('group_assignment_courses')
export class GroupAssignmentCourse extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  // Entity relationships
  @ManyToOne('GroupAssignment', 'groupAssignmentCourses', {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  group: any;

  @ManyToOne('Course', 'groupAssignmentCourses', { onDelete: 'CASCADE' })
  @JoinColumn()
  course: any;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
