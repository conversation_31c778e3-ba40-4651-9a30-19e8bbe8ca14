import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ThreadsService } from './threads.service';
import { CustomLogger } from '@src/common/logging';

@Controller('threads')
export class ThreadsController {
  private loggerMeta: any;

  constructor(
    private readonly threadsService: ThreadsService,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: ThreadsController.name };
  }

  @Get()
  async getAllThreads() {
    try {
      this.logger.log('Getting all threads', this.loggerMeta);
      return await this.threadsService.getAllThreads();
    } catch (error) {
      this.logger.error(
        `Error in getAllThreads: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('search')
  async searchThreads(@Query('q') searchTerm: string) {
    try {
      this.logger.log(
        `Searching threads with term: ${searchTerm}`,
        this.loggerMeta,
      );
      return await this.threadsService.searchThreads(searchTerm);
    } catch (error) {
      this.logger.error(
        `Error in searchThreads: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('with-message-count')
  async getThreadsWithMessageCount() {
    try {
      this.logger.log('Getting threads with message count', this.loggerMeta);
      return await this.threadsService.getThreadsWithMessageCount();
    } catch (error) {
      this.logger.error(
        `Error in getThreadsWithMessageCount: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':threadId')
  async getThreadById(@Param('threadId') threadId: string) {
    try {
      this.logger.log(`Getting thread by ID: ${threadId}`, this.loggerMeta);
      return await this.threadsService.getThreadById(threadId);
    } catch (error) {
      this.logger.error(
        `Error in getThreadById: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':threadId/messages')
  async getThreadMessages(@Param('threadId') threadId: string) {
    try {
      this.logger.log(
        `Getting messages for thread: ${threadId}`,
        this.loggerMeta,
      );
      return await this.threadsService.getThreadMessages(threadId);
    } catch (error) {
      this.logger.error(
        `Error in getThreadMessages: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post()
  async createThread(
    @Body() threadData: { title: string; description?: string },
  ) {
    try {
      this.logger.log(
        `Creating thread with title: ${threadData.title}`,
        this.loggerMeta,
      );
      return await this.threadsService.createThread(threadData);
    } catch (error) {
      this.logger.error(
        `Error in createThread: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':threadId/messages')
  async sendMessage(
    @Param('threadId') threadId: string,
    @Body() messageData: { content: string; senderId: string },
  ) {
    try {
      this.logger.log(
        `Sending message in thread: ${threadId}`,
        this.loggerMeta,
      );
      return await this.threadsService.sendMessage(threadId, messageData);
    } catch (error) {
      this.logger.error(
        `Error in sendMessage: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
